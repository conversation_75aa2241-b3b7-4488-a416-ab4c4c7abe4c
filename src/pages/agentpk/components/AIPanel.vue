<template>
  <div class="ai-panel-wrapper">
    <!-- 模型和问题显示区域 - 在面板顶部 -->
    <div class="model-question-info" :class="panelColorClass">
      <div class="info-line model-line" :class="panelColorClass">
        <span class="model-text">{{ modelName || title }}</span>
      </div>
      <div class="info-line">
        <span class="info-label">当前问题：</span>
        <span class="info-value">{{ currentQuestion || '暂无问题' }}</span>
      </div>
    </div>

    <div class="ai-panel">
      <!-- AI面板标题和控制区域 -->
      <div class="panel-header">
        <!-- 董会答介绍区域 -->
        <div class="assistant-intro">
          <div class="assistant-avatar">
            <img src="@/assets/assistant/董会答.png" alt="董会答" />
          </div>
          <div class="assistant-info">
            <div class="assistant-name">董会答</div>
            <div class="assistant-desc">懂你懂美团的问答助手</div>
          </div>
          <div class="assistant-actions">
            <div v-if="currentUserName" class="user-greeting">
              你好，{{ currentUserName }}
            </div>
            <button class="action-btn more-assistants" @click="$emit('go-back')">
              更多助手
            </button>
            <button class="action-btn toggle-memo" @click="toggleMemoSection">
              {{ showMemoSection ? '收起资料' : '展开资料' }}
            </button>
          </div>
        </div>

        <!-- 备忘录组件区域 -->
        <div v-if="showMemoSection" class="memo-section">
          <PersonalMemo
            ref="personalMemoRef"
            :user-id="currentUserId"
            :person-id="currentPersonId"
            @add-person-memo="$emit('add-person-memo')"
            @edit-person-memo="$emit('edit-person-memo', $event)"
            @delete-memo="$emit('delete-memo', $event)"
          />
          <IndustryMemo
            ref="industryMemoRef"
            @add-industry-memo="$emit('add-industry-memo')"
            @edit-industry-memo="$emit('edit-industry-memo', $event)"
            @delete-industry-memo="$emit('delete-industry-memo', $event)"
          />
          <CompanyKnowledgeBase @open-knowledge-dialog="$emit('open-knowledge-dialog')" />
          <PreInfo
            @add-pre-info="$emit('add-pre-info')"
          />
        </div>
      </div>

    <!-- 消息显示区域 -->
    <div class="messages-container" ref="messagesContainer">
      <!-- 使用AgentChatItem渲染所有消息 -->
      <template v-for="(message, index) in chatMessages" :key="message.key">
        <!-- 用户消息和AI回答使用AgentChatItem组件 -->
        <AgentChatItem
          v-if="message.role === 'user' || message.role === 'assistant'"
          :message-data="message"
          :original-question="getOriginalQuestionForMessage(message, index)"
          @send-message="handleChatItemMessage"
        />

        <!-- 思考过程组件 -->
        <AgentThinkingProcess
          v-if="message.role === 'thinking'"
          :thinking-data="'thinkingData' in message ? message.thinkingData : { items: [], isLoading: false }"
        />
      </template>
    </div>

      <!-- 子输入区域 -->
      <SubInputSection
        ref="subInputRef"
        @send-message="handleSubMessage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, computed } from 'vue';
import AgentThinkingProcess from './AgentThinkingProcess.vue';
import AgentChatItem from './AgentChatItem.vue';
import SubInputSection from './SubInputSection.vue';
import PersonalMemo from './PersonalMemo.vue';
import IndustryMemo from './IndustryMemo.vue';
import CompanyKnowledgeBase from './CompanyKnowledgeBase.vue';
import PreInfo from './PreInfo.vue';

interface IThinkingItem {
  type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result' | 'core_answer_start' | 'final_answer_complete';
  message?: string;
  step?: 'start' | 'processing' | 'complete';
  question?: string;
  questions?: string[];
  results?: Array<{ title: string; link: string }>;
}

interface IThinkingData {
  items: IThinkingItem[];
  isLoading: boolean;
}

interface IMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
  thinkingData?: IThinkingData; // 助手消息包含完整的思考过程
}

// 扩展的聊天消息类型，兼容AgentChatItem
type ExtendedChatMessage = IChatStreamContent | {
  role: 'thinking';
  key: string | number;
  thinkingData: IThinkingData;
};

interface IProps {
  title: string;
  modelName?: string; // 新增模型名称属性
  messages?: IMessage[];
  currentThinkingData?: IThinkingData;
  isLoading?: boolean;
  currentUserName?: string; // 当前用户名
  currentUserId?: string; // 当前用户ID
  currentPersonId?: string; // 当前人员ID
  currentQuestion?: string; // 当前问题
  panelIndex?: number; // 面板索引，用于区分颜色
}

const props = withDefaults(defineProps<IProps>(), {
  messages: () => [],
  currentThinkingData: () => ({ items: [], isLoading: false }),
  isLoading: false,
  modelName: '',
  currentUserName: '',
  currentUserId: '',
  currentPersonId: '',
  currentQuestion: '',
  panelIndex: 1,
});

const emit = defineEmits([
  'sub-message',
  'go-back',
  'add-person-memo',
  'edit-person-memo',
  'delete-memo',
  'add-industry-memo',
  'edit-industry-memo',
  'delete-industry-memo',
  'open-knowledge-dialog',
  'add-pre-info'
]);

// 备忘录区域显示状态（默认收起）
const showMemoSection = ref(false);

const messagesContainer = ref<HTMLElement | null>(null);
const subInputRef = ref<InstanceType<typeof SubInputSection> | null>(null);
const personalMemoRef = ref<InstanceType<typeof PersonalMemo> | null>(null);
const industryMemoRef = ref<InstanceType<typeof IndustryMemo> | null>(null);

// 切换备忘录区域显示
const toggleMemoSection = () => {
  showMemoSection.value = !showMemoSection.value;
};

// 根据面板索引计算颜色类
const panelColorClass = computed(() => {
  switch (props.panelIndex) {
    case 1:
      return 'panel-blue';
    case 2:
      return 'panel-yellow';
    case 3:
      return 'panel-purple';
    default:
      return 'panel-blue';
  }
});

// 将消息转换为AgentChatItem兼容的格式
const chatMessages = computed(() => {
  const result: ExtendedChatMessage[] = [];

  // 转换历史消息
  props.messages.forEach((message) => {
    if (message.type === 'user') {
      const userMessage: IChatStreamContent = {
        key: message.timestamp || Date.now(),
        role: 'user',
        content: message.content,
        isFinish: true,
        reasoningData: {
          content: '',
          status: 'completed',
        },
      };
      result.push(userMessage);
    } else if (message.type === 'assistant') {
      // 如果assistant消息有思考数据，先添加思考过程
      if (message.thinkingData && message.thinkingData.items.length > 0) {
        result.push({
          role: 'thinking',
          key: `thinking_${message.timestamp || Date.now()}`,
          thinkingData: message.thinkingData,
        });
      }

      // 然后添加assistant消息
      const assistantMessage: IChatStreamContent = {
        key: message.timestamp || Date.now(),
        role: 'assistant',
        content: message.content,
        isFinish: true,
        reasoningData: {
          content: '',
          status: 'completed',
        },
      };
      result.push(assistantMessage);
    }
  });

  // 添加当前思考过程（如果存在）
  if (props.currentThinkingData.items.length > 0) {
    result.push({
      role: 'thinking',
      key: `thinking_${Date.now()}`,
      thinkingData: props.currentThinkingData,
    });
  }

  return result;
});

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
};

// 处理子输入框的消息
const handleSubMessage = async (message: string) => {
  console.log('🚀 [AIPanel] 收到子输入框消息:', message);

  // 直接发送消息给父组件，不在这里处理主题生成
  emit('sub-message', message, props.panelIndex);
};

// 处理AgentChatItem发送的消息
const handleChatItemMessage = (message: string) => {
  console.log('🚀 [AIPanel] 收到AgentChatItem消息:', message);

  // 转发给父组件
  emit('sub-message', message, props.panelIndex);
};

// 获取消息的原始问题
const getOriginalQuestionForMessage = (_message: ExtendedChatMessage, index: number): string => {
  // 查找最近的用户消息作为原始问题
  for (let i = index - 1; i >= 0; i--) {
    const prevMessage = chatMessages.value[i];
    if (prevMessage && 'role' in prevMessage && prevMessage.role === 'user') {
      return prevMessage.content;
    }
  }

  // 如果没有找到，返回当前问题或默认值
  return props.currentQuestion || '用户的原始问题';
};

// 监听消息变化，自动滚动到底部
watch(
  () => [props.messages.length, props.currentThinkingData.items.length, props.isLoading],
  () => {
    scrollToBottom();
  },
  { deep: true }
);

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  subInputRef,
});
</script>

<style lang="scss" scoped>
.ai-panel-wrapper {
  display: flex;
  flex-direction: column;
  border-radius: 12px 12px 0 0;
  height: 100%;
  background: white;
}

// 模型和问题显示区域 - 在面板顶部
.model-question-info {
  padding: 12px 16px;
  border-radius: 12px 12px 0 0;
  border: 2px solid rgba(139, 126, 216, 0.3);
  border-bottom: none;

  // 不同面板的背景色
  &.panel-blue {
    background: #e3f2fd; // 淡蓝色
  }

  &.panel-yellow {
    background: #fff8e1; // 淡黄色
  }

  &.panel-purple {
    background: #f3e5f5; // 淡紫色
  }

  .info-line {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 16px; // 放大字体

    &:last-child {
      margin-bottom: 0;
    }

    // 模型行特殊样式
    &.model-line {
      justify-content: center; // 居中对齐
      margin-bottom: 12px; // 增加底部间距

      .model-text {
        font-size: 26px; // 放大字体
        font-weight: 500; // 加粗
        text-align: center;
      }

      // 深蓝色
      &.panel-blue .model-text {
        color: #092e59; // 深蓝色
      }

      // 深黄色
      &.panel-yellow .model-text {
        color: #5b2f08; // 深黄色
      }

      // 深紫色
      &.panel-purple .model-text {
        color: #6a1b9a; // 深紫色
      }
    }

    .info-label {
      font-weight: 600;
      color: #666;
      min-width: 80px;
    }

    .info-value {
      color: #333;
      flex: 1;
      word-break: break-all;
    }
  }
}

.ai-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(139, 126, 216, 0.3);
  border-radius: 0 0 16px 16px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.panel-header {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(139, 126, 216, 0.1) 0%, rgba(183, 148, 246, 0.1) 100%);
  border-bottom: 1px solid rgba(139, 126, 216, 0.2);

  // 董会答介绍区域
  .assistant-intro {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    background: #f3f9ff; // 更浅的蓝色背景
    padding: 8px;

    .assistant-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .assistant-info {
      flex: 1;

      .assistant-name {
        font-size: 20px; // 放大字体
        font-weight: 600;
        color: hsl(238, 57%, 25%);
      }

      .assistant-desc {
        font-size: 16px; // 放大字体
        color: #333;
      }
    }

    .assistant-actions {
      display: flex;
      align-items: center;
      gap: 6px;

      .user-greeting {
        font-size: 16px; // 放大字体
        font-weight: 500;
        color: hsl(238, 57%, 25%);
        margin-right: 6px;
      }

      .action-btn {
        padding: 6px 8px;
        border: none;
        color: hsl(238, 57%, 25%);
        font-size: 16px; // 放大字体
        background-color: transparent;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 6px;

        &:hover {
          background: rgba(139, 126, 216, 0.2);
        }
      }
    }
  }

  // 模型和问题显示区域已移到外层

  // 备忘录组件区域
  .memo-section {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    border-bottom: 1px solid rgba(139, 126, 216, 0.1);
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(139, 126, 216, 0.1);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(139, 126, 216, 0.3);
  border-radius: 3px;

  &:hover {
    background: rgba(139, 126, 216, 0.5);
  }
}
</style>
